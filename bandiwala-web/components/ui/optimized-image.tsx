'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { getImageUrl, getSafeFallbackImage, getImageLoadingStrategy } from '@/utils/imageUtils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fill?: boolean;
  priority?: boolean;
  fallbackType?: 'food' | 'vendor';
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * An optimized image component that handles 402 errors and other loading issues
 * by gracefully falling back to local images and using appropriate loading strategies
 */
export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  fill = false,
  priority = false,
  fallbackType = 'food',
  onLoad,
  onError
}: OptimizedImageProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 2;

  useEffect(() => {
    // Reset state when src changes
    setHasError(false);
    setIsLoading(true);
    setRetryCount(0);

    // If src is empty or invalid, use fallback immediately
    if (!src || src === 'undefined' || src === 'null') {
      setImageSrc(getSafeFallbackImage(fallbackType));
      setIsLoading(false);
      return;
    }

    // If it's already a local image, use it directly
    if (src.startsWith('/images/')) {
      setImageSrc(src);
      setIsLoading(false);
      return;
    }

    // For external images, get the full URL
    const fullImageUrl = getImageUrl(src);
    setImageSrc(fullImageUrl);
    setIsLoading(false);
  }, [src, fallbackType]);

  const handleError = () => {
    console.log(`Image failed to load: ${imageSrc}, retry count: ${retryCount}`);
    
    if (retryCount < maxRetries && !imageSrc.startsWith('/images/')) {
      // Try again with a slight delay
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        setImageSrc(getImageUrl(src)); // Retry with the same URL
      }, 1000 * (retryCount + 1)); // Exponential backoff
    } else {
      // Use fallback image
      console.log('Using fallback image after retries exhausted');
      setHasError(true);
      setImageSrc(getSafeFallbackImage(fallbackType));
    }
    
    onError?.();
  };

  const handleLoad = () => {
    console.log(`Image loaded successfully: ${imageSrc}`);
    setHasError(false);
    onLoad?.();
  };

  // Show loading placeholder while determining the image source
  if (isLoading) {
    return (
      <div 
        className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}
        style={fill ? {} : { width, height }}
      >
        <div className="text-gray-400 text-xs">Loading...</div>
      </div>
    );
  }

  // Get loading strategy based on the image
  const loadingStrategy = getImageLoadingStrategy(imageSrc);

  // Common props for the Image component
  const imageProps = {
    src: imageSrc,
    alt,
    className,
    onError: handleError,
    onLoad: handleLoad,
    priority: priority || loadingStrategy.priority,
    unoptimized: loadingStrategy.unoptimized,
    loading: loadingStrategy.loading,
    // Add quality setting for better optimization
    quality: hasError ? 75 : 85,
    // Add sizes for responsive images
    sizes: fill ? '100vw' : undefined,
  };

  if (fill) {
    return (
      <Image
        {...imageProps}
        fill
      />
    );
  }

  return (
    <Image
      {...imageProps}
      width={width || 400}
      height={height || 300}
    />
  );
}

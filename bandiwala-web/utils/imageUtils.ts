/**
 * Utility functions for handling image URLs
 */

// The base URL for the API server that serves images
const API_SERVER_URL = process.env.NEXT_PUBLIC_API_URL;

const DEFAULT_IMAGE = '/images/vendor.jpeg';

console.log('Image Utils: Using API_SERVER_URL:', API_SERVER_URL);

const PROBLEMATIC_PATTERNS = [
  'Jai%20Bhavani%20Chat%20',
  '<PERSON> Cha<PERSON> ',
  'rajamandri.jpeg',
  'bandiwala-items-pics/items/Jai%20Bhavani%20Chat%20',
  'bandiwala-items-pics/items/Jai Bhavani Chat ',
  '/bandiwala-items-pics/vendors/rajamandri.jpeg'
];

/**
 * Converts a relative image path to an absolute URL
 * @param imagePath The relative image path (e.g., "/images/vendor1.jpg")
 * @returns The absolute URL for the image
 */
export const getImageUrl = (imagePath: string): string => {
  console.log('getImageUrl called with:', imagePath);

  // If the path is already an absolute URL, return it as is
  if (imagePath && (imagePath.startsWith('http://') || imagePath.startsWith('https://'))) {
    console.log('Already absolute URL, returning as is');
    return imagePath;
  }
  if (!imagePath || imagePath === 'undefined' || imagePath === 'null') {
    console.log('Invalid path, using default image');
    return `${API_SERVER_URL}${DEFAULT_IMAGE}`;
  }

  // If the path starts with a slash, it's already properly formatted
  const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
  console.log('Normalized path:', normalizedPath);

  // For paths with spaces and special characters, encode each segment properly
  let encodedPath;
  if (normalizedPath.includes(' ') || normalizedPath.includes('(') || normalizedPath.includes(')')) {
    console.log('Path contains spaces/special chars, encoding segments');
    // Split by '/', encode each segment, then rejoin
    encodedPath = normalizedPath.split('/').map(segment => {
      return segment === '' ? '' : encodeURIComponent(segment);
    }).join('/');
    console.log('Encoded path:', encodedPath);
  } else {
    // For simple paths, use encodeURI
    encodedPath = encodeURI(normalizedPath);
    console.log('Simple encoding:', encodedPath);
  }

  // Always use the API server for images
  const finalUrl = `${API_SERVER_URL}${encodedPath}`;
  console.log('Final URL:', finalUrl);
  return finalUrl;
};

/**
 * Gets a safe fallback image based on the type
 * @param fallbackType The type of fallback image needed ('food' or 'vendor')
 * @returns The path to a safe fallback image
 */
export const getSafeFallbackImage = (fallbackType: 'food' | 'vendor' = 'food'): string => {
  const fallbackImages = {
    food: '/images/vendor.jpeg',
    vendor: '/images/default-vendor.jpg'
  };

  return fallbackImages[fallbackType] || fallbackImages.food;
};

/**
 * Checks if an image path contains problematic patterns that might cause loading issues
 * @param imagePath The image path to check
 * @returns True if the path contains problematic patterns
 */
export const isProblematicImage = (imagePath: string): boolean => {
  if (!imagePath) return false;

  return PROBLEMATIC_PATTERNS.some(pattern =>
    imagePath.includes(pattern)
  );
};

/**
 * Gets the appropriate loading strategy for an image
 * @param imagePath The image path
 * @returns Object with loading strategy properties
 */
export const getImageLoadingStrategy = (imagePath: string) => {
  const isProblematic = isProblematicImage(imagePath);
  const isExternal = imagePath && (imagePath.startsWith('http://') || imagePath.startsWith('https://'));

  return {
    unoptimized: isProblematic || isExternal,
    priority: false,
    loading: 'lazy' as const,
    placeholder: 'blur' as const,
    blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=='
  };
};